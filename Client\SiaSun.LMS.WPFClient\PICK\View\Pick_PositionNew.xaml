﻿<UserControl
    x:Class="SiaSun.LMS.WPFClient.PICK.View.Pick_PositionNew"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:i="http://schemas.microsoft.com/expression/2010/interactivity"
    xmlns:local="clr-namespace:SiaSun.LMS.WPFClient.PICK.View"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
    MinWidth="240"
    MinHeight="300"
    mc:Ignorable="d">
    <Grid>
        <Grid.Resources>
            <local:InformationBackgroundConverter
                x:Key="InformationHighlightBackgroundConverter"
                DefaultBrush="RoyalBlue"
                HighlightBrush="DeepPink" />

            <Style x:Key="GroupBoxStyle1" TargetType="{x:Type GroupBox}">
                <Setter Property="BorderBrush" Value="#D5DFE5" />
                <Setter Property="BorderThickness" Value="1" />
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type GroupBox}">
                            <Grid SnapsToDevicePixels="true">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="6" />
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="6" />
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" MinHeight="59" />
                                    <RowDefinition Height="*" />
                                    <RowDefinition Height="6" />
                                </Grid.RowDefinitions>
                                <Border
                                    Grid.Row="0"
                                    Grid.RowSpan="4"
                                    Grid.Column="0"
                                    Grid.ColumnSpan="4"
                                    Margin="0,-0.25,0,0.25"
                                    Background="#FFE5E5E5"
                                    BorderBrush="Transparent"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="4">
                                    <Border.Effect>
                                        <DropShadowEffect Direction="350" Color="#FFAAAAAA" />
                                    </Border.Effect>
                                </Border>
                                <Border
                                    x:Name="Header"
                                    Grid.Row="1"
                                    Grid.RowSpan="1"
                                    Grid.Column="1"
                                    Height="16.96"
                                    Margin="0"
                                    Padding="3,1,3,0"
                                    HorizontalAlignment="Right"
                                    VerticalAlignment="Top"
                                    Background="{x:Null}" />
                                <ContentPresenter
                                    Grid.Row="2"
                                    Grid.RowSpan="1"
                                    Grid.Column="1"
                                    Grid.ColumnSpan="2"
                                    Margin="{TemplateBinding Padding}"
                                    SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                                <Border
                                    Grid.Row="1"
                                    Grid.RowSpan="3"
                                    Grid.ColumnSpan="4"
                                    BorderBrush="White"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="4">
                                    <Border
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        CornerRadius="3">
                                        <Border
                                            BorderBrush="White"
                                            BorderThickness="{TemplateBinding BorderThickness}"
                                            CornerRadius="2" />
                                    </Border>
                                </Border>
                                <Grid
                                    x:Name="HeaderGrid"
                                    Grid.Row="1"
                                    Grid.RowSpan="1"
                                    Grid.Column="2"
                                    Grid.ColumnSpan="2"
                                    Height="47.2"
                                    Margin="0,7.982,-16,3.818"
                                    HorizontalAlignment="Right"
                                    VerticalAlignment="Stretch">
                                    <Path
                                        Width="12.29"
                                        Height="16.1"
                                        Margin="0,0,8.067,0"
                                        HorizontalAlignment="Right"
                                        VerticalAlignment="Top"
                                        Data="M12.19,0 L12.290733,14.847 -1.3000648E-08,14.847 z"
                                        RenderTransformOrigin="0.499999978361064,0.499999995889058"
                                        Stretch="Fill"
                                        Stroke="Black"
                                        StrokeThickness="0">
                                        <Path.Fill>
                                            <LinearGradientBrush StartPoint="0.5,0" EndPoint="0.466,2.201">
                                                <GradientStop Offset="1" Color="#C66A6A6A" />
                                                <GradientStop Offset="0.855" Color="#E1434343" />
                                                <GradientStop Offset="0.11" Color="#FFC6C6C6" />
                                            </LinearGradientBrush>
                                        </Path.Fill>
                                        <Path.RenderTransform>
                                            <TransformGroup>
                                                <ScaleTransform />
                                                <SkewTransform />
                                                <RotateTransform Angle="90.087" />
                                                <TranslateTransform X="6.0531771644038841" Y="-6.04399277075815" />
                                            </TransformGroup>
                                        </Path.RenderTransform>
                                    </Path>
                                    <Border
                                        Margin="0,8.061,0,0"
                                        Background="White"
                                        BorderBrush="Black"
                                        BorderThickness="0"
                                        CornerRadius="16,0,0,16">
                                        <Border.Effect>
                                            <DropShadowEffect
                                                BlurRadius="10"
                                                Direction="195"
                                                Opacity="0.305"
                                                ShadowDepth="6" />
                                        </Border.Effect>
                                        <Border
                                            x:Name="contentBorder"
                                            Margin="6,6,0,6"
                                            BorderBrush="Black"
                                            CornerRadius="16,0,0,16">
                                            <Border.Background>
                                                <LinearGradientBrush StartPoint="-0.024,0.502" EndPoint="1.002,0.498">
                                                    <!--<GradientStop Color="#FF678B03" Offset="0.027"/>
                                                    <GradientStop Color="#FFA4C43D" Offset="0.948"/>
                                                    <GradientStop Color="#FFADCA54" Offset="0.969"/>
                                                    <GradientStop Color="#FFA7C646" Offset="0.975"/>
                                                    <GradientStop Color="#FFC9EF4C" Offset="0.994"/>-->
                                                    <GradientStop Offset="0.027" Color="#FF87CEEB" />
                                                    <GradientStop Offset="0.948" Color="#FF87CEFF" />
                                                    <GradientStop Offset="0.969" Color="#FF87CEFA" />
                                                    <GradientStop Offset="0.975" Color="#FF7EC0EE" />
                                                    <GradientStop Offset="0.994" Color="#FF97FFFF" />
                                                </LinearGradientBrush>
                                            </Border.Background>
                                            <Grid>
                                                <ContentControl
                                                    Margin="20,0,23,0"
                                                    HorizontalAlignment="Left"
                                                    VerticalAlignment="Center"
                                                    Foreground="White"
                                                    d:LayoutOverrides="Height">
                                                    <ContentPresenter
                                                        Width="212.323"
                                                        Margin="0"
                                                        HorizontalAlignment="Stretch"
                                                        VerticalAlignment="Center"
                                                        ContentSource="Header"
                                                        RecognizesAccessKey="True"
                                                        SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                                                </ContentControl>
                                            </Grid>
                                        </Border>
                                    </Border>
                                </Grid>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
        </Grid.Resources>
        <GroupBox Header="{Binding PositionName}" Style="{StaticResource GroupBoxStyle1}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>
                <Border
                    Grid.Row="0"
                    Width="162"
                    Height="50"
                    Margin="2"
                    BorderBrush="Black"
                    BorderThickness="3">
                    <Rectangle Fill="{Binding Path=bPick, Converter={StaticResource InformationHighlightBackgroundConverter}}" Stretch="Fill">
                        <i:Interaction.Triggers>
                            <i:EventTrigger EventName="MouseUp">
                                <i:InvokeCommandAction Command="{Binding MouseUpCommand}" />
                            </i:EventTrigger>
                        </i:Interaction.Triggers>
                    </Rectangle>
                </Border>
                <TextBlock
                    Grid.Row="1"
                    Height="30"
                    Margin="2"
                    HorizontalAlignment="Center"
                    FontSize="20"
                    FontWeight="Bold"
                    Text="{Binding PositionStockBarcode}" />
                <!--<uc:ComboBoxQuery Margin="5" Grid.Row="2" FontSize="20" FontWeight="Bold" ComboBoxHeight="35" ComboBoxWidth="100"
                                  ButtonHeight="35" ButtonWidth="60" ButtonContent="查询计划"
                                  ComboBoxDataTable="{Binding PlanDataTable}"  HorizontalAlignment="Center"
                                  SelectItem="{Binding Plan_ID,Mode=TwoWay}"
                                  ComboBoxDisplayMemberPath="PLAN_CODE"
                                  ></uc:ComboBoxQuery>-->
                <ComboBox
                    Grid.Row="2"
                    Width="200"
                    Height="35"
                    Margin="2"
                    HorizontalAlignment="Center"
                    DisplayMemberPath="PLAN_CODE"
                    FontSize="16"
                    FontWeight="Bold"
                    ItemsSource="{Binding PlanDataTable}"
                    SelectedValue="{Binding Plan_ID, Mode=TwoWay}"
                    SelectedValuePath="PLAN_ID" />
                <StackPanel
                    Grid.Row="3"
                    Margin="2"
                    HorizontalAlignment="Center"
                    Orientation="Horizontal">
                    <Button
                        Width="60"
                        Height="25"
                        Margin="5,0,5,0"
                        Background="Pink"
                        BorderBrush="Ivory"
                        BorderThickness="1"
                        Command="{Binding CommandPick}"
                        FontSize="16"
                        FontWeight="Bold"
                        Style="{x:Null}">
                        清单
                    </Button>
                    <Button
                        Width="60"
                        Height="25"
                        Margin="5,0,5,0"
                        Background="GreenYellow"
                        BorderBrush="Ivory"
                        BorderThickness="1"
                        Command="{Binding CommandLock}"
                        FontSize="16"
                        FontWeight="Bold"
                        Style="{x:Null}">
                        未拣
                    </Button>
                    <Button
                        Width="60"
                        Height="25"
                        Margin="5,0,5,0"
                        Background="WhiteSmoke"
                        BorderBrush="Ivory"
                        BorderThickness="1"
                        Command="{Binding CmdPrintWBSContent}"
                        FontSize="16"
                        FontWeight="Bold"
                        Style="{x:Null}">
                        打印
                    </Button>
                    <Menu
                        Height="25"
                        MaxWidth="25"
                        IsMainMenu="True">
                        <MenuItem
                            Height="25"
                            Background="Red"
                            FontWeight="Bold"
                            Header="">
                            <!--<StackPanel VerticalAlignment="Center" Orientation="Horizontal">
                                <TextBox
                                    Width="60"
                                    Height="20"
                                    Margin="2"
                                    VerticalAlignment="Center"
                                    FontSize="12"
                                    Text="{Binding CheckStringForOperation, Mode=TwoWay}" />
                                <Button
                                    Width="80"
                                    Height="20"
                                    Margin="2"
                                    Command="{Binding CmdErrorOperationSubmit}"
                                    Content="提交"
                                    FontSize="12" />
                            </StackPanel>
                            <Separator />-->
                            <!--<MenuItem
                                Command="{Binding CmdErrorOperationUnBindOrder}"
                                Header="强制脱开"
                                IsEnabled="{Binding ErrerOperationShow}" />-->
                            <MenuItem
                                Command="{Binding CmdErrorOperationUnBindOrder}"
                                Header="强制脱开"
                                IsEnabled="True" />
                        </MenuItem>
                    </Menu>
                </StackPanel>
                <TextBlock
                    Grid.Row="4"
                    Height="30"
                    Margin="5,0,5,0"
                    HorizontalAlignment="Center"
                    FontSize="20"
                    FontWeight="Bold"
                    Text="{Binding PositionCode}" />
                <StackPanel
                    Grid.Row="5"
                    Margin="2"
                    HorizontalAlignment="Center"
                    Orientation="Horizontal">
                    <Button
                        Width="50"
                        Height="35"
                        Margin="0,0,5,0"
                        Background="Yellow"
                        BorderBrush="Ivory"
                        BorderThickness="1"
                        Command="{Binding CommandLockAll}"
                        FontSize="16"
                        FontWeight="Bold"
                        Style="{x:Null}">
                        锁定
                    </Button>
                    <Button
                        Width="50"
                        Height="35"
                        Margin="5,0,5,0"
                        Background="CadetBlue"
                        BorderBrush="Ivory"
                        BorderThickness="1"
                        Command="{Binding CommandOut}"
                        FontSize="16"
                        FontWeight="Bold"
                        Style="{x:Null}">
                        出库
                    </Button>
                    <Button
                        Width="50"
                        Height="35"
                        Margin="5,0,5,0"
                        Background="PaleVioletRed"
                        BorderBrush="Ivory"
                        BorderThickness="1"
                        Command="{Binding CommandLockPartly}"
                        FontSize="16"
                        FontWeight="Bold"
                        Style="{x:Null}">
                        缺锁
                    </Button>
                    <Button
                        Width="50"
                        Height="35"
                        Margin="5,0,0,0"
                        Background="LightGray"
                        BorderBrush="Ivory"
                        BorderThickness="1"
                        Command="{Binding CommandUnBind}"
                        FontSize="16"
                        FontWeight="Bold"
                        Style="{x:Null}">
                        解绑
                    </Button>
                </StackPanel>
            </Grid>
        </GroupBox>
    </Grid>
</UserControl>
