﻿<ad:DocumentContent
    x:Class="SiaSun.LMS.WPFClient.PICK.View.PickStationAutoBind2025CheckView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
    xmlns:cmd="SiaSun.LMS.WPFClient.MVVM"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:gridhelper="clr-namespace:SiaSun.LMS.WPFClient.PICK.Model"
    xmlns:i="http://schemas.microsoft.com/expression/2010/interactivity"
    xmlns:local="clr-namespace:SiaSun.LMS.WPFClient.PICK.View"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
    Title="PickStatioView"
    mc:Ignorable="d">

    <!-- 使用ViewBox包装整个内容以实现缩放适应 -->
    <Viewbox Stretch="Uniform" StretchDirection="Both">
        <Grid Width="1920" Height="1080">
            <Grid.Resources>
                <local:InformationBackgroundConverter
                    x:Key="InformationHighlightBackgroundConverter"
                    DefaultBrush="AliceBlue"
                    HighlightBrush="Yellow" />

                <local:VisiabilityConverter x:Key="VisibilityConverter" />
                <DataTemplate x:Key="BoxTypeADataTemplate">
                    <Grid MinWidth="300" MinHeight="150">
                    <Grid.RowDefinitions>
                        <RowDefinition MinHeight="100" />
                        <RowDefinition MaxHeight="60" />
                    </Grid.RowDefinitions>
                    <Grid
                        Grid.Row="0"
                        gridhelper:GridHelper.GridLineBrush="Black"
                        gridhelper:GridHelper.GridLineThickness="5"
                        gridhelper:GridHelper.ShowBorder="True">
                        <Label
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[0]}"
                            Content="1"
                            FontSize="16"
                            Foreground="Red"
                            Style="{x:Null}" />
                    </Grid>
                    <StackPanel
                        Grid.Row="1"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Orientation="Horizontal">
                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontSize="14"
                            FontWeight="Bold"
                            Text="{Binding Stock_barcode}" />
                    </StackPanel>
                </Grid>
            </DataTemplate>

            <DataTemplate x:Key="BoxTypeBDataTemplate">
                <Grid MinWidth="300" MinHeight="150">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" MinHeight="100" />
                        <RowDefinition Height="Auto" MaxHeight="30" />
                    </Grid.RowDefinitions>
                    <Grid
                        Grid.Row="0"
                        gridhelper:GridHelper.GridLineBrush="Black"
                        gridhelper:GridHelper.GridLineThickness="5"
                        gridhelper:GridHelper.ShowBorder="True">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Label
                            Grid.Column="0"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[0]}"
                            Content="1"
                            FontSize="16"
                            Foreground="Red"
                            Style="{x:Null}" />
                        <Label
                            Grid.Column="1"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[1]}"
                            Content="2"
                            FontSize="16"
                            Foreground="Red"
                            Style="{x:Null}" />
                    </Grid>
                    <StackPanel
                        Grid.Row="1"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Orientation="Horizontal">
                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontSize="14"
                            FontWeight="Bold"
                            Text="{Binding Stock_barcode}" />
                    </StackPanel>
                </Grid>
                <!--<box:Box_B></box:Box_B>-->
            </DataTemplate>

            <DataTemplate x:Key="BoxTypeCDataTemplate">
                <Grid MinWidth="300" MinHeight="150">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" MinHeight="100" />
                        <RowDefinition Height="Auto" MaxHeight="30" />
                    </Grid.RowDefinitions>
                    <Grid
                        Grid.Row="0"
                        gridhelper:GridHelper.GridLineBrush="Black"
                        gridhelper:GridHelper.GridLineThickness="5"
                        gridhelper:GridHelper.ShowBorder="True">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Label
                            Grid.Column="0"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[0]}"
                            Content="1"
                            FontSize="16"
                            Foreground="Red"
                            Style="{x:Null}" />
                        <Label
                            Grid.Column="1"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[1]}"
                            Content="2"
                            FontSize="16"
                            Foreground="Red"
                            Style="{x:Null}" />
                        <Label
                            Grid.Column="2"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[2]}"
                            Content="3"
                            FontSize="16"
                            Foreground="Red"
                            Style="{x:Null}" />
                    </Grid>
                    <StackPanel
                        Grid.Row="1"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Orientation="Horizontal">
                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontSize="14"
                            FontWeight="Bold"
                            Text="{Binding Stock_barcode}" />
                    </StackPanel>
                </Grid>
            </DataTemplate>

            <DataTemplate x:Key="BoxTypeDDataTemplate">
                <Grid MinWidth="300" MinHeight="150">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" MinHeight="100" />
                        <RowDefinition Height="Auto" MaxHeight="30" />
                    </Grid.RowDefinitions>
                    <Grid
                        Grid.Row="0"
                        gridhelper:GridHelper.GridLineBrush="Black"
                        gridhelper:GridHelper.GridLineThickness="5"
                        gridhelper:GridHelper.ShowBorder="True">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <!--<Rectangle Grid.Row="0" Grid.Column="0" Fill="LightCyan"/>
                        <Rectangle Grid.Row="0" Grid.Column="1" Fill="LightBlue"/>
                        <Rectangle Grid.Row="1" Grid.Column="0" Fill="LightBlue"/>
                        <Rectangle Grid.Row="1" Grid.Column="1" Fill="LightCyan"/>-->
                        <Label
                            Grid.Row="0"
                            Grid.Column="0"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[0]}"
                            Content="1"
                            FontSize="16"
                            Foreground="Red"
                            Style="{x:Null}" />
                        <Label
                            Grid.Row="0"
                            Grid.Column="1"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[1]}"
                            Content="2"
                            FontSize="16"
                            Foreground="Red"
                            Style="{x:Null}" />
                        <Label
                            Grid.Row="1"
                            Grid.Column="0"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[2]}"
                            Content="3"
                            FontSize="16"
                            Foreground="Red"
                            Style="{x:Null}" />
                        <Label
                            Grid.Row="1"
                            Grid.Column="1"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[3]}"
                            Content="4"
                            FontSize="16"
                            Foreground="Red"
                            Style="{x:Null}" />
                    </Grid>
                    <StackPanel
                        Grid.Row="1"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Orientation="Horizontal">
                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontSize="14"
                            FontWeight="Bold"
                            Text="{Binding Stock_barcode}" />
                    </StackPanel>
                </Grid>
                <!--<box:Box_B></box:Box_B>-->
            </DataTemplate>

            <DataTemplate x:Key="BoxTypeEDataTemplate">
                <Grid MinWidth="300" MinHeight="150">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" MinHeight="100" />
                        <RowDefinition Height="Auto" MaxHeight="30" />
                    </Grid.RowDefinitions>
                    <Grid
                        Grid.Row="0"
                        gridhelper:GridHelper.GridLineBrush="Black"
                        gridhelper:GridHelper.GridLineThickness="5"
                        gridhelper:GridHelper.ShowBorder="True">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <Label
                            Grid.Row="0"
                            Grid.Column="0"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[0]}"
                            Content="1"
                            FontSize="20"
                            Foreground="Red"
                            Style="{x:Null}" />
                        <Label
                            Grid.Row="0"
                            Grid.Column="1"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[1]}"
                            Content="2"
                            FontSize="20"
                            Foreground="Red"
                            Style="{x:Null}" />
                        <Label
                            Grid.Row="0"
                            Grid.Column="2"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[2]}"
                            Content="3"
                            FontSize="20"
                            Foreground="Red"
                            Style="{x:Null}" />
                        <Label
                            Grid.Row="1"
                            Grid.Column="0"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[3]}"
                            Content="4"
                            FontSize="20"
                            Foreground="Red"
                            Style="{x:Null}" />
                        <Label
                            Grid.Row="1"
                            Grid.Column="1"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[4]}"
                            Content="5"
                            FontSize="20"
                            Foreground="Red"
                            Style="{x:Null}" />
                        <Label
                            Grid.Row="1"
                            Grid.Column="2"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[5]}"
                            Content="6"
                            FontSize="20"
                            Foreground="Red"
                            Style="{x:Null}" />
                    </Grid>
                    <StackPanel
                        Grid.Row="1"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Orientation="Horizontal">
                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontSize="20"
                            FontWeight="Bold"
                            Text="{Binding Stock_barcode}" />
                    </StackPanel>
                </Grid>
            </DataTemplate>

            <!--  二期相反布局  -->
            <DataTemplate x:Key="BoxTypeARDataTemplate">
                <Grid MinWidth="300" MinHeight="150">
                    <Grid.RowDefinitions>
                        <RowDefinition MinHeight="100" />
                        <RowDefinition MaxHeight="60" />
                    </Grid.RowDefinitions>
                    <Grid
                        Grid.Row="0"
                        gridhelper:GridHelper.GridLineBrush="Black"
                        gridhelper:GridHelper.GridLineThickness="5"
                        gridhelper:GridHelper.ShowBorder="True">
                        <Label
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[0]}"
                            Content="1"
                            FontSize="20"
                            Foreground="Red"
                            Style="{x:Null}" />
                    </Grid>
                    <StackPanel
                        Grid.Row="1"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Orientation="Horizontal">
                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontSize="20"
                            FontWeight="Bold"
                            Text="{Binding Stock_barcode}" />
                    </StackPanel>
                </Grid>
            </DataTemplate>

            <DataTemplate x:Key="BoxTypeBRDataTemplate">
                <Grid MinWidth="300" MinHeight="150">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" MinHeight="100" />
                        <RowDefinition Height="Auto" MaxHeight="30" />
                    </Grid.RowDefinitions>
                    <Grid
                        Grid.Row="0"
                        gridhelper:GridHelper.GridLineBrush="Black"
                        gridhelper:GridHelper.GridLineThickness="5"
                        gridhelper:GridHelper.ShowBorder="True">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Label
                            Grid.Column="0"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[1]}"
                            Content="2"
                            FontSize="20"
                            Foreground="Red"
                            Style="{x:Null}" />
                        <Label
                            Grid.Column="1"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[0]}"
                            Content="1"
                            FontSize="20"
                            Foreground="Red"
                            Style="{x:Null}" />
                    </Grid>
                    <StackPanel
                        Grid.Row="1"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Orientation="Horizontal">
                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontSize="20"
                            FontWeight="Bold"
                            Text="{Binding Stock_barcode}" />
                    </StackPanel>
                </Grid>
                <!--<box:Box_B></box:Box_B>-->
            </DataTemplate>

            <DataTemplate x:Key="BoxTypeCRDataTemplate">
                <Grid MinWidth="300" MinHeight="150">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" MinHeight="100" />
                        <RowDefinition Height="Auto" MaxHeight="30" />
                    </Grid.RowDefinitions>
                    <Grid
                        Grid.Row="0"
                        gridhelper:GridHelper.GridLineBrush="Black"
                        gridhelper:GridHelper.GridLineThickness="5"
                        gridhelper:GridHelper.ShowBorder="True">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Label
                            Grid.Column="0"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[2]}"
                            Content="3"
                            FontSize="20"
                            Foreground="Red"
                            Style="{x:Null}" />
                        <Label
                            Grid.Column="1"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[1]}"
                            Content="2"
                            FontSize="20"
                            Foreground="Red"
                            Style="{x:Null}" />
                        <Label
                            Grid.Column="2"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[0]}"
                            Content="1"
                            FontSize="20"
                            Foreground="Red"
                            Style="{x:Null}" />
                    </Grid>
                    <StackPanel
                        Grid.Row="1"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Orientation="Horizontal">
                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontSize="20"
                            FontWeight="Bold"
                            Text="{Binding Stock_barcode}" />
                    </StackPanel>
                </Grid>
            </DataTemplate>

            <DataTemplate x:Key="BoxTypeDRDataTemplate">
                <Grid MinWidth="300" MinHeight="150">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" MinHeight="100" />
                        <RowDefinition Height="Auto" MaxHeight="30" />
                    </Grid.RowDefinitions>
                    <Grid
                        Grid.Row="0"
                        gridhelper:GridHelper.GridLineBrush="Black"
                        gridhelper:GridHelper.GridLineThickness="5"
                        gridhelper:GridHelper.ShowBorder="True">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <!--<Rectangle Grid.Row="0" Grid.Column="0" Fill="LightCyan"/>
                        <Rectangle Grid.Row="0" Grid.Column="1" Fill="LightBlue"/>
                        <Rectangle Grid.Row="1" Grid.Column="0" Fill="LightBlue"/>
                        <Rectangle Grid.Row="1" Grid.Column="1" Fill="LightCyan"/>-->
                        <Label
                            Grid.Row="0"
                            Grid.Column="0"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[3]}"
                            Content="4"
                            FontSize="20"
                            Foreground="Red"
                            Style="{x:Null}" />
                        <Label
                            Grid.Row="0"
                            Grid.Column="1"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[2]}"
                            Content="3"
                            FontSize="20"
                            Foreground="Red"
                            Style="{x:Null}" />
                        <Label
                            Grid.Row="1"
                            Grid.Column="0"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[1]}"
                            Content="2"
                            FontSize="20"
                            Foreground="Red"
                            Style="{x:Null}" />
                        <Label
                            Grid.Row="1"
                            Grid.Column="1"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[0]}"
                            Content="1"
                            FontSize="20"
                            Foreground="Red"
                            Style="{x:Null}" />
                    </Grid>
                    <StackPanel
                        Grid.Row="1"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Orientation="Horizontal">
                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontSize="20"
                            FontWeight="Bold"
                            Text="{Binding Stock_barcode}" />
                    </StackPanel>
                </Grid>
                <!--<box:Box_B></box:Box_B>-->
            </DataTemplate>

            <DataTemplate x:Key="BoxTypeERDataTemplate">
                <Grid MinWidth="300" MinHeight="150">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" MinHeight="100" />
                        <RowDefinition Height="Auto" MaxHeight="30" />
                    </Grid.RowDefinitions>
                    <Grid
                        Grid.Row="0"
                        gridhelper:GridHelper.GridLineBrush="Black"
                        gridhelper:GridHelper.GridLineThickness="5"
                        gridhelper:GridHelper.ShowBorder="True">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <Label
                            Grid.Row="0"
                            Grid.Column="0"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[5]}"
                            Content="6"
                            FontSize="20"
                            Foreground="Red"
                            Style="{x:Null}" />
                        <Label
                            Grid.Row="0"
                            Grid.Column="1"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[4]}"
                            Content="5"
                            FontSize="20"
                            Foreground="Red"
                            Style="{x:Null}" />
                        <Label
                            Grid.Row="0"
                            Grid.Column="2"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[3]}"
                            Content="4"
                            FontSize="20"
                            Foreground="Red"
                            Style="{x:Null}" />
                        <Label
                            Grid.Row="1"
                            Grid.Column="0"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[2]}"
                            Content="3"
                            FontSize="20"
                            Foreground="Red"
                            Style="{x:Null}" />
                        <Label
                            Grid.Row="1"
                            Grid.Column="1"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[1]}"
                            Content="2"
                            FontSize="20"
                            Foreground="Red"
                            Style="{x:Null}" />
                        <Label
                            Grid.Row="1"
                            Grid.Column="2"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{Binding PartBackGround[0]}"
                            Content="1"
                            FontSize="20"
                            Foreground="Red"
                            Style="{x:Null}" />
                    </Grid>
                    <StackPanel
                        Grid.Row="1"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Orientation="Horizontal">
                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontSize="20"
                            FontWeight="Bold"
                            Text="{Binding Stock_barcode}" />
                    </StackPanel>
                </Grid>
            </DataTemplate>
            <!--    -->
            <DataTemplate x:Key="PickPositionTemplate">
                <local:Pick_Position
                    Margin="2"
                    Padding="5"
                    HorizontalAlignment="Stretch" />
            </DataTemplate>

            <DataTemplate x:Key="InformationTemplate">
                <Border
                    Margin="2"
                    Padding="2"
                    Background="Ivory"
                    BorderBrush="DarkMagenta"
                    BorderThickness="2"
                    CornerRadius="5,5,5,5">
                    <StackPanel Orientation="Horizontal">
                        <Label
                            Width="Auto"
                            MinWidth="120"
                            Height="Auto"
                            MinHeight="25"
                            Margin="3,1,3,1"
                            Background="{Binding Path=IsHighLight, Converter={StaticResource InformationHighlightBackgroundConverter}}"
                            Content="{Binding Msg}"
                            FontSize="12"
                            FontWeight="Bold"
                            Style="{x:Null}" />
                        <TextBlock
                            Height="Auto"
                            MinHeight="25"
                            Margin="3,1,3,1"
                            FontSize="12"
                            Text="拣选位置:" />
                        <TextBlock
                            Height="Auto"
                            MinHeight="25"
                            MinWidth="80"
                            Margin="3,1,3,1"
                            FontSize="12"
                            Text="{Binding Title}" />
                        <TextBlock
                            Height="Auto"
                            MinHeight="25"
                            Margin="3,1,3,1"
                            FontSize="12"
                            Text="目标位置:" />
                        <TextBlock
                            Height="Auto"
                            MinHeight="25"
                            MinWidth="80"
                            Margin="3,1,3,1"
                            FontSize="12"
                            Text="{Binding Remark}" />
                        <TextBlock
                            Height="Auto"
                            MinHeight="25"
                            Margin="3,1,3,1"
                            FontSize="12"
                            Text="物料编码:" />
                        <TextBlock
                            Height="Auto"
                            MinHeight="25"
                            MinWidth="140"
                            Margin="3,1,3,1"
                            FontSize="12"
                            Text="{Binding SubTitle}" />
                        <TextBlock
                            Height="Auto"
                            MinHeight="25"
                            Margin="3,1,3,1"
                            FontSize="12"
                            Text="物料名称:" />
                        <TextBlock
                            Height="Auto"
                            MinHeight="25"
                            MinWidth="160"
                            Margin="3,1,3,1"
                            FontSize="12"
                            Text="{Binding Header}" />
                        <TextBlock
                            Height="Auto"
                            MinHeight="25"
                            Margin="3,1,3,1"
                            FontSize="12"
                            FontWeight="Bold"
                            Foreground="Crimson"
                            Text="拣选数量:" />
                        <TextBlock
                            Height="Auto"
                            MinHeight="25"
                            Margin="3,1,3,1"
                            FontSize="12"
                            Foreground="Crimson"
                            Text="{Binding Content}" />
                    </StackPanel>
                </Border>
            </DataTemplate>
        </Grid.Resources>
        <Grid.RowDefinitions>
            <RowDefinition Height="0.15*" MinHeight="80" />
            <RowDefinition Height="0.25*" MinHeight="180" />
            <RowDefinition Height="0.1*" MinHeight="60" />
            <RowDefinition Height="0.45*" MinHeight="300" />
            <RowDefinition Height="0.05*" MinHeight="30" />
        </Grid.RowDefinitions>
        <StackPanel Grid.Row="0" Orientation="Vertical">
            <ToolBar
                Height="Auto"
                MinHeight="40"
                VerticalContentAlignment="Center"
                ToolBarTray.IsLocked="True">
                <Label
                    Height="Auto"
                    MinHeight="25"
                    Margin="3"
                    Content="{Binding LoginStatus}"
                    FontSize="12"
                    FontWeight="Bold" />
                <Button
                    Width="Auto"
                    MinWidth="70"
                    Height="Auto"
                    MinHeight="28"
                    Margin="3"
                    Command="{Binding CmdConnectNdLogin}"
                    Content="申请接单"
                    FontSize="11"
                    IsEnabled="{Binding ConnectLoginBtnIsEnabled}"
                    ToolTip="申请接受拣选订单" />
                <Label
                    Height="Auto"
                    MinHeight="25"
                    Margin="3"
                    Content="{Binding StationName}"
                    FontSize="12"
                    FontWeight="Bold" />
                <!--<uc:ComboBoxQuery Margin="10,2,2,2"  FontSize="16" FontWeight="Bold" ComboBoxHeight="25" ComboBoxWidth="180"
                ButtonHeight="27" ButtonWidth="90" ButtonContent="查询计划"  ComboBoxIsEditable="True" ComboBoxIsTextSearchCaseSensitive="True"
                ComboBoxDataTable="{Binding OrderDataTable}"  HorizontalAlignment="Center"
                SelectItem="{Binding PlanGroup,Mode=TwoWay}"
                ComboBoxDisplayMemberPath="NAME"
                ></uc:ComboBoxQuery>-->
                <Label
                    Height="Auto"
                    MinHeight="25"
                    Margin="3"
                    Content="已绑定唯一码:"
                    FontSize="12"
                    FontWeight="Bold" />
                <Label
                    Height="Auto"
                    MinHeight="25"
                    Margin="3"
                    Content="{Binding PlanGroup, Mode=OneWay}"
                    FontSize="12"
                    FontWeight="Bold" />
                <!--<ComboBox Margin="15,5,5,5" FontSize="16" FontWeight="Bold" MinWidth="90" Height="35" HorizontalAlignment="Center"
                ItemsSource="{Binding OrderDataTable}" SelectedValue="{Binding PlanGroupToBind,Mode=TwoWay}"
                SelectedValuePath="VALUE" DisplayMemberPath="NAME"></ComboBox>-->
                <Button
                    Height="Auto"
                    MinHeight="28"
                    MinWidth="70"
                    Margin="3"
                    Command="{Binding CmdBindOrder}"
                    Content="确认绑定"
                    FontSize="11"
                    ToolTip="将推送至本拣选工作站的唯一码绑定" />
                <Button
                    Height="Auto"
                    MinHeight="28"
                    MinWidth="70"
                    Margin="3"
                    Command="{Binding CmdLockAndOutOrder}"
                    Content="拣选锁定(完全)"
                    FontSize="11"
                    ToolTip="完全锁定绑定该拣选工作站的订单并生成锁定库存，当存在库存短缺情况时，提示锁定失败"
                    Visibility="Collapsed" />
                <Button
                    Height="Auto"
                    MinHeight="28"
                    MinWidth="70"
                    Margin="3"
                    Command="{Binding CmdManageDownOutOrder}"
                    Content="拣选出库"
                    FontSize="11"
                    ToolTip="根据锁定库存下达下架出库任务"
                    Visibility="Collapsed" />
                <!--<Button Content="拣选任务取消" Height="35" Margin="5" Command="{Binding CmdA}" Visibility="Collapsed"/>
                <Button Content="拣选箱离开" Height="35"  Margin="5" Command="{Binding CmdB}" Visibility="Collapsed"/>
                <Button Content="删除" Height="35" Margin="5" Command="{Binding CmdDelete}" Visibility="Collapsed"/>-->
                <Button
                    Height="35"
                    MinWidth="90"
                    Margin="5"
                    Command="{Binding CmdShowBindInformation}"
                    Content="订单绑定信息" />
                <Button
                    Height="35"
                    MinWidth="90"
                    Margin="5"
                    Command="{Binding CmdShowStorageInformation}"
                    Content="拣选箱库存" />
                <Button
                    Height="35"
                    MinWidth="90"
                    Margin="5"
                    Command="{Binding CmdManagePickList}"
                    Content="拣选任务列表" />
                <Button
                    Height="35"
                    MinWidth="90"
                    Margin="5"
                    Command="{Binding CmdLockAndOutOrderPartly}"
                    Content="拣选锁定(缺料)"
                    ToolTip="部分锁定绑定该拣选工作站的订单并生成锁定库存，当存在库存短缺情况时，依旧可以锁定成功"
                    Visibility="Collapsed" />
                <Button
                    Height="35"
                    MinWidth="90"
                    Margin="5"
                    Command="{Binding CmdUnBindOrder}"
                    Content="订单解绑"
                    Visibility="Collapsed" />
                <Button
                    Height="35"
                    MinWidth="90"
                    Margin="5"
                    Command="{Binding CmdPrintManagePickContent}"
                    Content="打印物料标签" />
                <Button
                    Height="35"
                    MinWidth="60"
                    Margin="5"
                    Command="{Binding CmdClose}"
                    Content="关闭" />
                <!--<Button Content="异常处理" MinWidth="60" Height="35" Margin="5" Command="{Binding CmdErrorOperation}"></Button>
                <Button Content="强制脱开并解锁" MinWidth="90" Height="35" Margin="5" Command="{Binding CmdErrorOperation}"></Button>
                <TextBox Height="35" Width="60"></TextBox>-->
                <Menu
                    Height="35"
                    Margin="5"
                    FontSize="15"
                    FontWeight="Bold"
                    Visibility="Collapsed">
                    <MenuItem
                        Height="35"
                        Background="OrangeRed"
                        Header="异常处理">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Margin="2" FontSize="14">验证码:</TextBlock>
                            <TextBox
                                Height="35"
                                MinWidth="50"
                                Margin="2"
                                FontSize="14"
                                Text="{Binding CheckStringForOperation, Mode=TwoWay}" />
                            <Button
                                Width="70"
                                Height="35"
                                MinWidth="30"
                                Command="{Binding CmdErrorOperationSubmit}"
                                Content="提交"
                                FontSize="14" />
                        </StackPanel>
                        <MenuItem
                            Command="{Binding CmdErrorOperationUnBindOrder}"
                            Header="强制脱开"
                            IsEnabled="{Binding ErrerOperationShow}" />
                        <MenuItem
                            Command="{Binding CmdErrorOperationUnLock}"
                            Header="解锁库存"
                            IsEnabled="{Binding ErrerOperationShow}" />
                    </MenuItem>
                </Menu>
                <Button
                    Height="35"
                    MinWidth="90"
                    Margin="5"
                    Background="SpringGreen"
                    Command="{Binding CmdShowPrevBinding}"
                    Content="预绑定"
                    Style="{x:Null}"
                    Visibility="Collapsed" />

            </ToolBar>

            <ToolBar
                Height="50"
                VerticalContentAlignment="Center"
                ToolBarTray.IsLocked="True"
                Visibility="{Binding IsShowPrevToolBar, Converter={StaticResource VisibilityConverter}}">
                <Label
                    Height="30"
                    Margin="5"
                    Content="预绑定唯一码:"
                    FontSize="16"
                    FontWeight="Bold"
                    Foreground="DeepPink" />
                <Label
                    Height="30"
                    Margin="5"
                    Content="{Binding PrevBindingPlanGroup, Mode=OneWay}"
                    FontSize="16"
                    FontWeight="Bold"
                    Foreground="Yellow" />
                <ComboBox
                    Height="35"
                    MinWidth="90"
                    Margin="15,5,5,5"
                    HorizontalAlignment="Center"
                    DisplayMemberPath="NAME"
                    FontSize="16"
                    FontWeight="Bold"
                    ItemsSource="{Binding PrevBindingOrderDataTable}"
                    SelectedValue="{Binding PlanGroupToPrevBinding, Mode=TwoWay}"
                    SelectedValuePath="VALUE" />

                <Button
                    Height="35"
                    MinWidth="90"
                    Margin="5"
                    Command="{Binding CmdPrevBinding}"
                    Content="锁定-出库" />
                <!--<Button Content="待用" MinWidth="90" Height="35" Margin="5" Command="{Binding CmdManagePickList}"></Button>-->
            </ToolBar>
        </StackPanel>

        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="0.15*" MinWidth="120" />
                <ColumnDefinition Width="0.5*" MinWidth="300" />
                <ColumnDefinition Width="0.15*" MinWidth="120" />
                <ColumnDefinition Width="0.2*" MinWidth="150" />
            </Grid.ColumnDefinitions>
            <StackPanel
                Grid.Column="0"
                HorizontalAlignment="Right"
                Orientation="Horizontal">
                <Image
                    Margin="10"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Center"
                    FlowDirection="{Binding StationFlowDirection}"
                    Source=".\Arrow.png"
                    Stretch="None"
                    Style="{x:Null}" />
                <!--<Image Margin="10" Style="{x:Null}" VerticalAlignment="Center"  Source=".\Arrow.png"/>
                <Image Margin="10" Style="{x:Null}" VerticalAlignment="Center"  Source=".\Arrow.png"/>-->
            </StackPanel>
            <ItemsControl
                Grid.Column="1"
                Margin="10"
                VerticalAlignment="Center"
                HorizontalContentAlignment="Center"
                ItemsSource="{Binding Boxs}">
                <ItemsControl.ItemTemplateSelector>
                    <local:BoxTypeDataTemplateSelector
                        BoxTypeADataTemplate="{StaticResource BoxTypeADataTemplate}"
                        BoxTypeARDataTemplate="{StaticResource BoxTypeARDataTemplate}"
                        BoxTypeBDataTemplate="{StaticResource BoxTypeBDataTemplate}"
                        BoxTypeBRDataTemplate="{StaticResource BoxTypeBRDataTemplate}"
                        BoxTypeCDataTemplate="{StaticResource BoxTypeCDataTemplate}"
                        BoxTypeCRDataTemplate="{StaticResource BoxTypeCRDataTemplate}"
                        BoxTypeDDataTemplate="{StaticResource BoxTypeDDataTemplate}"
                        BoxTypeDRDataTemplate="{StaticResource BoxTypeDRDataTemplate}"
                        BoxTypeEDataTemplate="{StaticResource BoxTypeEDataTemplate}"
                        BoxTypeERDataTemplate="{StaticResource BoxTypeERDataTemplate}" />
                </ItemsControl.ItemTemplateSelector>
            </ItemsControl>
            <StackPanel
                Grid.Column="2"
                HorizontalAlignment="Left"
                Orientation="Horizontal">
                <Image
                    Margin="10"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Center"
                    FlowDirection="{Binding StationFlowDirection}"
                    Source=".\Arrow.png"
                    Stretch="None"
                    Style="{x:Null}" />
                <!--<Image Margin="10" Style="{x:Null}" VerticalAlignment="Center" Source=".\Arrow.png"/>
                <Image Margin="10" Style="{x:Null}" VerticalAlignment="Center" Source=".\Arrow.png"/>-->
            </StackPanel>
            <StackPanel
                Grid.Column="3"
                HorizontalAlignment="Left"
                Orientation="Vertical"
                Visibility="Visible">
                <StackPanel Orientation="Horizontal">
                    <TextBlock
                        Margin="3"
                        FontSize="12"
                        FontWeight="Bold">
                        SN:
                    </TextBlock>
                    <TextBox
                        Width="Auto"
                        MinWidth="100"
                        Margin="3"
                        FontSize="12"
                        Text="{Binding ScanSN, Mode=TwoWay}" />
                </StackPanel>
                <StackPanel Orientation="Horizontal">
                    <Button
                        Height="Auto"
                        MinHeight="28"
                        MinWidth="40"
                        Margin="3"
                        Command="{Binding CmdAddSNToList}"
                        Content="添加"
                        FontSize="11" />
                    <Button
                        Height="Auto"
                        MinHeight="28"
                        MinWidth="40"
                        Margin="3"
                        Command="{Binding CmdClearSNList}"
                        Content="清空"
                        FontSize="11" />
                    <Button
                        Height="Auto"
                        MinHeight="28"
                        MinWidth="40"
                        Margin="3"
                        Command="{Binding CmdConfirmSNList}"
                        Content="提交"
                        FontSize="11" />
                </StackPanel>
                <ListView
                    Width="Auto"
                    MinWidth="140"
                    MinHeight="40"
                    MaxHeight="80"
                    Margin="3"
                    FontSize="12"
                    FontWeight="Bold"
                    ItemsSource="{Binding SNList}" />
            </StackPanel>
        </Grid>

        <!--<ScrollViewer Grid.Row="2" MaxHeight="100" Margin="5,10,5,10">-->
        <ItemsControl
            Grid.Row="2"
            MaxHeight="100"
            Margin="30,10,30,10"
            VerticalAlignment="Center"
            HorizontalContentAlignment="Center"
            ItemTemplate="{StaticResource InformationTemplate}"
            ItemsSource="{Binding Informations}">
            <!--  ScrollViewer.VerticalScrollBarVisibility="Visible" ScrollViewer.CanContentScroll="True">  -->
        </ItemsControl>
        <!--</ScrollViewer>-->

        <Grid Grid.Row="3" Margin="5,20,5,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <local:Pick_PositionNew
                Grid.Column="0"
                Margin="5"
                Padding="5"
                VerticalAlignment="Center"
                DataContext="{Binding PickPositionViewModels[4]}" />
            <local:Pick_PositionNew
                Grid.Column="1"
                Margin="5"
                Padding="5"
                VerticalAlignment="Center"
                DataContext="{Binding PickPositionViewModels[3]}" />
            <local:Pick_PositionNew
                Grid.Column="2"
                Margin="5"
                Padding="5"
                VerticalAlignment="Center"
                DataContext="{Binding PickPositionViewModels[2]}" />
            <local:Pick_PositionNew
                Grid.Column="3"
                Margin="5"
                Padding="5"
                VerticalAlignment="Center"
                DataContext="{Binding PickPositionViewModels[1]}" />
            <local:Pick_PositionNew
                Grid.Column="4"
                Margin="5"
                Padding="5"
                VerticalAlignment="Center"
                DataContext="{Binding PickPositionViewModels[0]}" />
        </Grid>

        <StackPanel
            Grid.Row="4"
            Margin="5,10,5,10"
            HorizontalAlignment="Center"
            Orientation="Vertical"
            Visibility="Collapsed">
            <Label
                Content="{Binding PlanHanderText, Mode=OneWay}"
                FontSize="16"
                FontWeight="Bold" />
        </StackPanel>
        <!--<ItemsControl Grid.Row="3" ItemsSource="{Binding PickPositionViewModels[0]}" ItemTemplate="{StaticResource PickPositionTemplate}" >
        <ItemsControl.ItemsPanel>
        <ItemsPanelTemplate>
        <StackPanel Orientation="Horizontal" ></StackPanel>
        </ItemsPanelTemplate>
        </ItemsControl.ItemsPanel>
        </ItemsControl>-->

        <MediaElement
            Name="mediaElementDingDan"
            LoadedBehavior="Manual"
            Source="PICK/Resources/dingdan.mp3"
            Stretch="Fill"
            UnloadedBehavior="Stop"
            Visibility="Collapsed"
            Volume="100" />
        </Grid>
    </Viewbox>
</ad:DocumentContent>
